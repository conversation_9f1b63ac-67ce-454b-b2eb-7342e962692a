"use client";

import { motion } from "framer-motion";

export default function SkillCard({
  skill,
  index
}: {
  skill: string;
  index: number;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      whileHover={{ 
        scale: 1.05,
        backgroundColor: "var(--foreground)",
        color: "var(--background)",
        borderColor: "var(--purple-500)",
      }}
      className="p-4 border border-gray-300 dark:border-gray-700 text-center transition-default cursor-pointer"
    >
      {skill}
    </motion.div>
  );
}
"use client";

import { motion } from "framer-motion";

export default function ProjectCard({
  item
}: {
  item: number;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: item * 0.1 }}
      viewport={{ once: true }}
      whileHover={{ y: -10 }}
      className="relative overflow-hidden border border-gray-300 dark:border-gray-700"
    >
      <div className="h-64 bg-gray-200 dark:bg-gray-800 w-full flex items-center justify-center">
        <div className="text-4xl font-bold text-gray-400 dark:text-gray-600">PROJECT {item}</div>
      </div>
      <div className="p-6 bg-white dark:bg-black bg-opacity-90 absolute bottom-0 left-0 right-0 transform translate-y-full hover:translate-y-0 transition-default border-t border-purple-500">
        <h3 className="text-xl font-bold mb-2">Project Title</h3>
        <p className="text-gray-600 dark:text-gray-400">Next.js, Tailwind CSS</p>
      </div>
    </motion.div>
  );
}
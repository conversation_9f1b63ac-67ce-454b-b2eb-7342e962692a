import type { Metada<PERSON> } from "next";
import { Space_Grotesk } from "next/font/google";
import "./globals.css";

const spaceGrotesk = Space_Grotesk({
  variable: "--font-space-grotesk",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "<PERSON> | Full-Stack Developer",
  description: "Personal portfolio of <PERSON>, a freelance full-stack software and web developer based in Arusha, Tanzania.",
  icons: {
    icon: '/favicon.svg',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${spaceGrotesk.variable} font-sans antialiased bg-white text-black dark:bg-black dark:text-white bg-purple-gradient`}
      >
        {children}
      </body>
    </html>
  );
}
import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { Skill } from '@/lib/entities';

export async function GET(request: NextRequest) {
  try {
    const dataSource = await getDatabase();
    const skillRepository = dataSource.getRepository(Skill);
    
    const skills = await skillRepository.find({
      where: { isActive: true },
      order: { sortOrder: 'ASC', createdAt: 'DESC' }
    });

    return NextResponse.json({ success: true, data: skills });
  } catch (error) {
    console.error('Get public skills error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { Experience } from '@/lib/entities';
import { getUserFromRequest } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const dataSource = await getDatabase();
    const experienceRepository = dataSource.getRepository(Experience);
    
    const experience = await experienceRepository.find({
      order: { sortOrder: 'ASC', startDate: 'DESC' }
    });

    return NextResponse.json({ success: true, data: experience });
  } catch (error) {
    console.error('Get experience error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { company, position, description, startDate, endDate, isCurrent, location, technologies, achievements, companyUrl, companyLogo, sortOrder, isActive } = body;

    if (!company || !position || !startDate) {
      return NextResponse.json({ error: 'Company, position, and start date are required' }, { status: 400 });
    }

    const dataSource = await getDatabase();
    const experienceRepository = dataSource.getRepository(Experience);

    const experience = experienceRepository.create({
      company,
      position,
      description,
      startDate: new Date(startDate),
      endDate: endDate ? new Date(endDate) : undefined,
      isCurrent: isCurrent || false,
      location,
      technologies: technologies || [],
      achievements: achievements || [],
      companyUrl,
      companyLogo,
      sortOrder: sortOrder || 0,
      isActive: isActive !== undefined ? isActive : true
    });

    const savedExperience = await experienceRepository.save(experience);
    return NextResponse.json({ success: true, data: savedExperience }, { status: 201 });
  } catch (error) {
    console.error('Create experience error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

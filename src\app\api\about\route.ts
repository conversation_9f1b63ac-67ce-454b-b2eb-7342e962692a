import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { About } from '@/lib/entities';

export async function GET(request: NextRequest) {
  try {
    const dataSource = await getDatabase();
    const aboutRepository = dataSource.getRepository(About);
    
    const about = await aboutRepository.findOne({
      where: { isActive: true }
    });

    return NextResponse.json({ success: true, data: about });
  } catch (error) {
    console.error('Get public about error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

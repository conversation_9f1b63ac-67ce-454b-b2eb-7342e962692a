import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { Experience } from '@/lib/entities';

export async function GET(request: NextRequest) {
  try {
    const dataSource = await getDatabase();
    const experienceRepository = dataSource.getRepository(Experience);
    
    const experience = await experienceRepository.find({
      where: { isActive: true },
      order: { sortOrder: 'ASC', startDate: 'DESC' }
    });

    return NextResponse.json({ success: true, data: experience });
  } catch (error) {
    console.error('Get public experience error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

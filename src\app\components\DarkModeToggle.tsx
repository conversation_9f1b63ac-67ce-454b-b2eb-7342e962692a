"use client";

import { motion } from "framer-motion";

export default function DarkModeToggle({
  darkMode,
  toggleDarkMode
}: {
  darkMode: boolean;
  toggleDarkMode: () => void;
}) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 1, duration: 1 }}
      className="absolute bottom-10 cursor-pointer z-10"
      onClick={toggleDarkMode}
    >
      <div className="w-12 h-6 rounded-full border border-gray-300 flex items-center p-1">
        <motion.div 
          className={`w-4 h-4 rounded-full ${darkMode ? "bg-purple-400" : "bg-gray-300"}`}
          animate={{ 
            x: darkMode ? 24 : 0,
            backgroundColor: darkMode ? "#a78bfa" : "#d4d4d4"
          }}
          transition={{ type: "spring", stiffness: 700, damping: 30 }}
        />
      </div>
    </motion.div>
  );
}
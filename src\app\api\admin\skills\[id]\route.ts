import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { Skill } from '@/lib/entities';
import { getUserFromRequest } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const dataSource = await getDatabase();
    const skillRepository = dataSource.getRepository(Skill);
    
    const skill = await skillRepository.findOne({
      where: { id: parseInt(params.id) }
    });

    if (!skill) {
      return NextResponse.json({ error: 'Skill not found' }, { status: 404 });
    }

    return NextResponse.json({ success: true, data: skill });
  } catch (error) {
    console.error('Get skill error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, icon, proficiency, category, sortOrder, isActive } = body;

    if (!name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    const dataSource = await getDatabase();
    const skillRepository = dataSource.getRepository(Skill);

    const skill = await skillRepository.findOne({
      where: { id: parseInt(params.id) }
    });

    if (!skill) {
      return NextResponse.json({ error: 'Skill not found' }, { status: 404 });
    }

    skill.name = name;
    skill.description = description;
    skill.icon = icon;
    skill.proficiency = proficiency || 0;
    skill.category = category || 'technical';
    skill.sortOrder = sortOrder || 0;
    skill.isActive = isActive !== undefined ? isActive : true;

    const updatedSkill = await skillRepository.save(skill);
    return NextResponse.json({ success: true, data: updatedSkill });
  } catch (error) {
    console.error('Update skill error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const dataSource = await getDatabase();
    const skillRepository = dataSource.getRepository(Skill);

    const skill = await skillRepository.findOne({
      where: { id: parseInt(params.id) }
    });

    if (!skill) {
      return NextResponse.json({ error: 'Skill not found' }, { status: 404 });
    }

    await skillRepository.remove(skill);
    return NextResponse.json({ success: true, message: 'Skill deleted successfully' });
  } catch (error) {
    console.error('Delete skill error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface Contact {
  id: number;
  name: string;
  email: string;
  phone?: string;
  subject?: string;
  message: string;
  status: string;
  notes?: string;
  isImportant: boolean;
  repliedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export default function ContactPage() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');

  useEffect(() => {
    fetchContacts();
  }, []);

  const fetchContacts = async () => {
    try {
      const response = await fetch('/api/admin/contact');
      if (response.ok) {
        const data = await response.json();
        setContacts(data.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch contacts:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'unread': return 'bg-red-500';
      case 'read': return 'bg-yellow-500';
      case 'replied': return 'bg-green-500';
      case 'archived': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const filteredContacts = contacts.filter(contact => {
    if (filter === 'all') return true;
    return contact.status === filter;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Contact Messages</h1>
          <p className="text-gray-400 mt-2">Manage messages from your portfolio visitors</p>
        </div>
        
        <div className="flex space-x-2">
          {['all', 'unread', 'read', 'replied', 'archived'].map((status) => (
            <button
              key={status}
              onClick={() => setFilter(status)}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                filter === status 
                  ? 'bg-purple-600 text-white' 
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {filteredContacts.length === 0 ? (
        <div className="bg-gray-900 border border-gray-800 rounded-lg p-8 text-center">
          <p className="text-gray-400">
            {filter === 'all' ? 'No messages found' : `No ${filter} messages found`}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredContacts.map((contact, index) => (
            <motion.div
              key={contact.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-gray-900 border border-gray-800 rounded-lg p-6"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div>
                    <h3 className="font-semibold text-lg flex items-center">
                      {contact.name}
                      {contact.isImportant && (
                        <span className="ml-2 text-yellow-500">⭐</span>
                      )}
                    </h3>
                    <p className="text-gray-400 text-sm">{contact.email}</p>
                    {contact.phone && (
                      <p className="text-gray-400 text-sm">{contact.phone}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 text-xs rounded ${getStatusColor(contact.status)} text-white`}>
                    {contact.status}
                  </span>
                  <span className="text-xs text-gray-500">
                    {new Date(contact.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>

              {contact.subject && (
                <div className="mb-3">
                  <p className="font-medium text-gray-300">Subject: {contact.subject}</p>
                </div>
              )}

              <div className="mb-4">
                <p className="text-gray-300 whitespace-pre-wrap">{contact.message}</p>
              </div>

              {contact.notes && (
                <div className="mb-4 p-3 bg-gray-800 rounded">
                  <p className="text-sm text-gray-400">
                    <strong>Notes:</strong> {contact.notes}
                  </p>
                </div>
              )}

              <div className="flex justify-between items-center">
                <div className="text-xs text-gray-500">
                  Received: {new Date(contact.createdAt).toLocaleString()}
                  {contact.repliedAt && (
                    <span className="ml-4">
                      Replied: {new Date(contact.repliedAt).toLocaleString()}
                    </span>
                  )}
                </div>
                
                <div className="flex space-x-2">
                  <a
                    href={`mailto:${contact.email}?subject=Re: ${contact.subject || 'Your message'}&body=Hi ${contact.name},%0D%0A%0D%0AThank you for your message.%0D%0A%0D%0ABest regards,%0D%0ACharles Peter Changawa`}
                    className="text-blue-400 hover:text-blue-300 text-sm"
                  >
                    Reply
                  </a>
                  <button className="text-yellow-400 hover:text-yellow-300 text-sm">
                    {contact.isImportant ? 'Unmark' : 'Mark Important'}
                  </button>
                  <button className="text-gray-400 hover:text-gray-300 text-sm">
                    Archive
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}

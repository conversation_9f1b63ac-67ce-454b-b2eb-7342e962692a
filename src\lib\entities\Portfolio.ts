import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('portfolio')
export class Portfolio {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  title!: string;

  @Column('text')
  description!: string;

  @Column({ nullable: true })
  shortDescription?: string;

  @Column({ nullable: true })
  image?: string;

  @Column('simple-json', { nullable: true })
  images?: string[];

  @Column({ nullable: true })
  projectUrl?: string;

  @Column({ nullable: true })
  githubUrl?: string;

  @Column('simple-json', { nullable: true })
  technologies?: string[];

  @Column({ nullable: true })
  category?: string;

  @Column({ nullable: true })
  client?: string;

  @Column({ type: 'date', nullable: true })
  completedDate?: Date;

  @Column({ default: 0 })
  sortOrder!: number;

  @Column({ default: true })
  isActive!: boolean;

  @Column({ default: false })
  isFeatured!: boolean;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}

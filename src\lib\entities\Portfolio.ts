import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('portfolio')
export class Portfolio {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column('varchar')
  title!: string;

  @Column('text')
  description!: string;

  @Column('text', { nullable: true })
  shortDescription?: string;

  @Column('varchar', { nullable: true })
  image?: string;

  @Column('simple-json', { nullable: true })
  images?: string[];

  @Column('varchar', { nullable: true })
  projectUrl?: string;

  @Column('varchar', { nullable: true })
  githubUrl?: string;

  @Column('simple-json', { nullable: true })
  technologies?: string[];

  @Column('varchar', { nullable: true })
  category?: string;

  @Column('varchar', { nullable: true })
  client?: string;

  @Column('date', { nullable: true })
  completedDate?: Date;

  @Column('integer', { default: 0 })
  sortOrder!: number;

  @Column('boolean', { default: true })
  isActive!: boolean;

  @Column('boolean', { default: false })
  isFeatured!: boolean;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}

"use client";

import { motion } from "framer-motion";

export default function ExperienceItem({
  exp,
  index
}: {
  exp: {
    company: string;
    role: string;
    period: string;
  };
  index: number;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, x: -30 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: index * 0.2 }}
      viewport={{ once: true }}
      className="relative pl-16 pb-12"
    >
      {/* Circle */}
      <div className="absolute left-0 top-2 w-8 h-8 rounded-full border-2 border-purple-500 bg-white dark:bg-black flex items-center justify-center">
        <div className="w-3 h-3 rounded-full bg-purple-500"></div>
      </div>
      
      <h3 className="text-2xl font-bold">{exp.company}</h3>
      <p className="text-lg text-gray-600 dark:text-gray-400">{exp.role}</p>
      <p className="text-gray-500 dark:text-gray-500">{exp.period}</p>
    </motion.div>
  );
}
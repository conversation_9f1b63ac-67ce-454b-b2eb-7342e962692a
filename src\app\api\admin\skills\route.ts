import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { Skill } from '@/lib/entities';
import { getUserFromRequest } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const dataSource = await getDatabase();
    const skillRepository = dataSource.getRepository(Skill);
    
    const skills = await skillRepository.find({
      order: { sortOrder: 'ASC', createdAt: 'DESC' }
    });

    return NextResponse.json({ success: true, data: skills });
  } catch (error) {
    console.error('Get skills error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, icon, proficiency, category, sortOrder, isActive } = body;

    if (!name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    const dataSource = await getDatabase();
    const skillRepository = dataSource.getRepository(Skill);

    const skill = skillRepository.create({
      name,
      description,
      icon,
      proficiency: proficiency || 0,
      category: category || 'technical',
      sortOrder: sortOrder || 0,
      isActive: isActive !== undefined ? isActive : true
    });

    const savedSkill = await skillRepository.save(skill);
    return NextResponse.json({ success: true, data: savedSkill }, { status: 201 });
  } catch (error) {
    console.error('Create skill error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

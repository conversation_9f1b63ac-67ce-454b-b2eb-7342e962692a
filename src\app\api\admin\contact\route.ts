import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { Contact } from '@/lib/entities';
import { getUserFromRequest } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const dataSource = await getDatabase();
    const contactRepository = dataSource.getRepository(Contact);
    
    const contacts = await contactRepository.find({
      order: { createdAt: 'DESC' }
    });

    return NextResponse.json({ success: true, data: contacts });
  } catch (error) {
    console.error('Get contacts error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, phone, subject, message } = body;

    if (!name || !email || !message) {
      return NextResponse.json({ error: 'Name, email, and message are required' }, { status: 400 });
    }

    const dataSource = await getDatabase();
    const contactRepository = dataSource.getRepository(Contact);

    const contact = contactRepository.create({
      name,
      email,
      phone,
      subject,
      message,
      status: 'unread',
      isImportant: false
    });

    const savedContact = await contactRepository.save(contact);
    return NextResponse.json({ success: true, data: savedContact }, { status: 201 });
  } catch (error) {
    console.error('Create contact error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';

interface Skill {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  proficiency: number;
  category: string;
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function SkillsPage() {
  const [skills, setSkills] = useState<Skill[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteId, setDeleteId] = useState<number | null>(null);

  useEffect(() => {
    fetchSkills();
  }, []);

  const fetchSkills = async () => {
    try {
      const response = await fetch('/api/admin/skills');
      if (response.ok) {
        const data = await response.json();
        setSkills(data.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch skills:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this skill?')) return;

    try {
      const response = await fetch(`/api/admin/skills/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setSkills(skills.filter(skill => skill.id !== id));
      } else {
        alert('Failed to delete skill');
      }
    } catch (error) {
      console.error('Delete error:', error);
      alert('Failed to delete skill');
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'technical': return 'bg-blue-500';
      case 'design': return 'bg-green-500';
      case 'soft': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Skills Management</h1>
          <p className="text-gray-400 mt-2">Manage your technical and soft skills</p>
        </div>
        <Link
          href="/admin/skills/new"
          className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded transition-colors"
        >
          Add New Skill
        </Link>
      </div>

      {skills.length === 0 ? (
        <div className="bg-gray-900 border border-gray-800 rounded-lg p-8 text-center">
          <p className="text-gray-400 mb-4">No skills found</p>
          <Link
            href="/admin/skills/new"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded transition-colors inline-block"
          >
            Add Your First Skill
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {skills.map((skill, index) => (
            <motion.div
              key={skill.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-gray-900 border border-gray-800 rounded-lg p-6"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  {skill.icon && <span className="text-2xl mr-3">{skill.icon}</span>}
                  <div>
                    <h3 className="font-semibold text-lg">{skill.name}</h3>
                    <div className="flex items-center mt-1">
                      <span className={`px-2 py-1 text-xs rounded ${getCategoryColor(skill.category)} text-white`}>
                        {skill.category}
                      </span>
                      {!skill.isActive && (
                        <span className="ml-2 px-2 py-1 text-xs rounded bg-red-500 text-white">
                          Inactive
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {skill.description && (
                <p className="text-gray-400 text-sm mb-4">{skill.description}</p>
              )}

              <div className="mb-4">
                <div className="flex justify-between text-sm mb-1">
                  <span>Proficiency</span>
                  <span>{skill.proficiency}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${skill.proficiency}%` }}
                  ></div>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">
                  Order: {skill.sortOrder}
                </span>
                <div className="flex space-x-2">
                  <Link
                    href={`/admin/skills/${skill.id}/edit`}
                    className="text-blue-400 hover:text-blue-300 text-sm"
                  >
                    Edit
                  </Link>
                  <button
                    onClick={() => handleDelete(skill.id)}
                    className="text-red-400 hover:text-red-300 text-sm"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}

import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { User, Skill, About, Service, Portfolio, Experience, Contact } from './entities';

export const AppDataSource = new DataSource({
  type: 'sqlite',
  database: 'portfolio.db',
  synchronize: true, // Set to false in production
  logging: process.env.NODE_ENV === 'development',
  entities: [User, Skill, About, Service, Portfolio, Experience, Contact],
  migrations: ['src/lib/migrations/*.ts'],
  subscribers: ['src/lib/subscribers/*.ts'],
});

let isInitialized = false;

export const initializeDatabase = async () => {
  if (!isInitialized) {
    try {
      await AppDataSource.initialize();
      console.log('Database connection initialized successfully');
      isInitialized = true;
      
      // Create default admin user if it doesn't exist
      await createDefaultAdmin();
    } catch (error) {
      console.error('Error during database initialization:', error);
      throw error;
    }
  }
  return AppDataSource;
};

export const getDatabase = async () => {
  if (!isInitialized) {
    await initializeDatabase();
  }
  return AppDataSource;
};

const createDefaultAdmin = async () => {
  try {
    const userRepository = AppDataSource.getRepository(User);
    const existingAdmin = await userRepository.findOne({ where: { username: 'admin' } });
    
    if (!existingAdmin) {
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const admin = userRepository.create({
        username: 'admin',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        isActive: true,
      });
      
      await userRepository.save(admin);
      console.log('Default admin user created successfully');
    }
  } catch (error) {
    console.error('Error creating default admin:', error);
  }
};

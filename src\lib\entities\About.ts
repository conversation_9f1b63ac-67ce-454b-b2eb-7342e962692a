import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('about')
export class About {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column('varchar')
  title!: string;

  @Column('text')
  description!: string;

  @Column('text', { nullable: true })
  shortBio?: string;

  @Column('varchar', { nullable: true })
  profileImage?: string;

  @Column('varchar', { nullable: true })
  location?: string;

  @Column('varchar', { nullable: true })
  email?: string;

  @Column('varchar', { nullable: true })
  phone?: string;

  @Column('varchar', { nullable: true })
  website?: string;

  @Column('simple-json', { nullable: true })
  socialLinks?: { platform: string; url: string }[];

  @Column('boolean', { default: true })
  isActive!: boolean;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}

@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #000000;
  --gray-100: #f5f5f5;
  --gray-200: #e5e5e5;
  --gray-300: #d4d4d4;
  --gray-400: #a3a3a3;
  --gray-500: #737373;
  --gray-600: #525252;
  --gray-700: #404040;
  --gray-800: #262626;
  --gray-900: #171717;
  --transition-default: all 0.3s ease;
  --purple-500: #8b5cf6;
  --purple-600: #7c3aed;
  --purple-900: #4c1d95;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #000000;
    --foreground: #ffffff;
    --gray-100: #171717;
    --gray-200: #262626;
    --gray-300: #404040;
    --gray-400: #525252;
    --gray-500: #737373;
    --gray-600: #a3a3a3;
    --gray-700: #d4d4d4;
    --gray-800: #e5e5e5;
    --gray-900: #f5f5f5;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-space-grotesk), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  transition: var(--transition-default);
}

/* Custom utility classes */
.transition-default {
  transition: var(--transition-default);
}

.grayscale-hover:hover {
  filter: grayscale(0%);
}

.grayscale {
  filter: grayscale(100%);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-600);
}

/* Purple gradient background */
.bg-purple-gradient {
  background: linear-gradient(135deg, var(--purple-900), var(--background));
}

/* Animation for scroll indicator */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) translateX(-50%);
  }
  40% {
    transform: translateY(-20px) translateX(-50%);
  }
  60% {
    transform: translateY(-10px) translateX(-50%);
  }
}

.animate-bounce {
  animation: bounce 2s infinite;
}
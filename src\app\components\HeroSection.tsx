"use client";

import { motion } from "framer-motion";
import Typewriter from "@/app/components/Typewriter";
import HeroButton from "@/app/components/HeroButton";
import PixelBlast from "@/app/components/PixelBlast";
import DarkModeToggle from "@/app/components/DarkModeToggle";

export default function HeroSection({
  darkMode,
  toggleDarkMode
}: {
  darkMode: boolean;
  toggleDarkMode: () => void;
}) {
  return (
    <section id="home" className="min-h-screen flex flex-col items-center justify-center p-8 text-center relative pt-16 overflow-hidden">
      <div className="absolute inset-0 z-0">
        <div style={{ width: '100%', height: '600px', position: 'relative' }}>
          <PixelBlast
            variant="circle"
            pixelSize={6}
            color="#B19EEF"
            patternScale={3}
            patternDensity={1.2}
            pixelSizeJitter={0.5}
            enableRipples
            rippleSpeed={0.4}
            rippleThickness={0.12}
            rippleIntensityScale={1.5}
            liquid
            liquidStrength={0.12}
            liquidRadius={1.2}
            liquidWobbleSpeed={5}
            speed={0.6}
            edgeFade={0.25}
            transparent
          />
        </div>
        <div className="absolute inset-0 bg-gradient-to-b from-purple-900/20 to-black/40 dark:from-purple-900/30 dark:to-black/60"></div>
      </div>
      
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="max-w-4xl relative z-10 flex flex-col md:flex-row items-center gap-12"
      >
        {/* Profile Image */}
        <div className="relative">
          <div className="w-64 h-64 rounded-full overflow-hidden border-4 border-purple-500/30 shadow-2xl">
            <img 
              src="/chaz.jpg" 
              alt="Charles Peter Changawa" 
              className="w-full h-full object-cover"
            />
          </div>
          <div className="absolute -bottom-2 -right-2 bg-purple-600 text-white px-4 py-1 rounded-full text-sm font-bold">
            CRAQINHO DIAWARA
          </div>
        </div>
        
        {/* Text Content */}
        <div className="text-left">
          <h1 className="text-4xl md:text-6xl font-bold mb-4 tracking-tight text-white">
            CHARLES PETER CHANGAWA
          </h1>
          <div className="text-xl md:text-2xl mb-6 text-gray-200 font-light">
            <Typewriter text="Full-Stack Developer | Graphic Designer | Video Director" />
          </div>
          <p className="text-lg text-gray-300 mb-8 max-w-2xl">
            I'm a versatile creative professional based in Arusha, Tanzania, specializing in building elegant, 
            performant, and scalable web applications. With expertise in both frontend and backend technologies, 
            I bring ideas to life through clean code and stunning visuals.
          </p>
          <div className="flex flex-wrap gap-4">
            <HeroButton />
            <motion.button
              whileHover={{ 
                scale: 1.05,
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 border-2 border-white/30 font-medium text-lg rounded-none transition-default text-white"
            >
              Download CV
            </motion.button>
          </div>
        </div>
      </motion.div>
      
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-gray-400"
      >
        <div className="flex flex-col items-center">
          <span className="text-sm mb-2">SCROLL TO EXPLORE</span>
          <div className="w-8 h-8 border-l-2 border-b-2 border-gray-400 transform rotate-[-45deg] animate-bounce"></div>
        </div>
      </motion.div>
      
      <DarkModeToggle darkMode={darkMode} toggleDarkMode={toggleDarkMode} />
    </section>
  );
}
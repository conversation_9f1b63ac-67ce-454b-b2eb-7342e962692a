"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';

interface Service {
  id: number;
  title: string;
  description: string;
  price?: string;
  features?: string[];
  icon?: string;
  image?: string;
  type: string;
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      const response = await fetch('/api/admin/services');
      if (response.ok) {
        const data = await response.json();
        setServices(data.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch services:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this service?')) return;

    try {
      const response = await fetch(`/api/admin/services/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setServices(services.filter(service => service.id !== id));
      } else {
        alert('Failed to delete service');
      }
    } catch (error) {
      console.error('Delete error:', error);
      alert('Failed to delete service');
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'package': return 'bg-blue-500';
      case 'service': return 'bg-green-500';
      case 'consultation': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Services Management</h1>
          <p className="text-gray-400 mt-2">Manage your service packages and offerings</p>
        </div>
        <Link
          href="/admin/services/new"
          className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded transition-colors"
        >
          Add New Service
        </Link>
      </div>

      {services.length === 0 ? (
        <div className="bg-gray-900 border border-gray-800 rounded-lg p-8 text-center">
          <p className="text-gray-400 mb-4">No services found</p>
          <Link
            href="/admin/services/new"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded transition-colors inline-block"
          >
            Add Your First Service
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-gray-900 border border-gray-800 rounded-lg p-6"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  {service.icon && <span className="text-2xl mr-3">{service.icon}</span>}
                  <div>
                    <h3 className="font-semibold text-lg">{service.title}</h3>
                    <div className="flex items-center mt-1">
                      <span className={`px-2 py-1 text-xs rounded ${getTypeColor(service.type)} text-white`}>
                        {service.type}
                      </span>
                      {!service.isActive && (
                        <span className="ml-2 px-2 py-1 text-xs rounded bg-red-500 text-white">
                          Inactive
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <p className="text-gray-400 text-sm mb-4 line-clamp-3">{service.description}</p>

              {service.price && (
                <div className="mb-4">
                  <span className="text-2xl font-bold text-purple-400">{service.price}</span>
                </div>
              )}

              {service.features && service.features.length > 0 && (
                <div className="mb-4">
                  <p className="text-sm text-gray-500 mb-2">Features:</p>
                  <ul className="text-sm text-gray-400 space-y-1">
                    {service.features.slice(0, 3).map((feature, idx) => (
                      <li key={idx} className="flex items-center">
                        <span className="w-1 h-1 bg-purple-500 rounded-full mr-2"></span>
                        {feature}
                      </li>
                    ))}
                    {service.features.length > 3 && (
                      <li className="text-gray-500">+{service.features.length - 3} more...</li>
                    )}
                  </ul>
                </div>
              )}

              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">
                  Order: {service.sortOrder}
                </span>
                <div className="flex space-x-2">
                  <Link
                    href={`/admin/services/${service.id}/edit`}
                    className="text-blue-400 hover:text-blue-300 text-sm"
                  >
                    Edit
                  </Link>
                  <button
                    onClick={() => handleDelete(service.id)}
                    className="text-red-400 hover:text-red-300 text-sm"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}

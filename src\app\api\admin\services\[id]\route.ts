import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { Service } from '@/lib/entities';
import { getUserFromRequest } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const dataSource = await getDatabase();
    const serviceRepository = dataSource.getRepository(Service);
    
    const service = await serviceRepository.findOne({
      where: { id: parseInt(params.id) }
    });

    if (!service) {
      return NextResponse.json({ error: 'Service not found' }, { status: 404 });
    }

    return NextResponse.json({ success: true, data: service });
  } catch (error) {
    console.error('Get service error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { title, description, price, features, icon, image, type, sortOrder, isActive } = body;

    if (!title || !description) {
      return NextResponse.json({ error: 'Title and description are required' }, { status: 400 });
    }

    const dataSource = await getDatabase();
    const serviceRepository = dataSource.getRepository(Service);

    const service = await serviceRepository.findOne({
      where: { id: parseInt(params.id) }
    });

    if (!service) {
      return NextResponse.json({ error: 'Service not found' }, { status: 404 });
    }

    service.title = title;
    service.description = description;
    service.price = price;
    service.features = features || [];
    service.icon = icon;
    service.image = image;
    service.type = type || 'package';
    service.sortOrder = sortOrder || 0;
    service.isActive = isActive !== undefined ? isActive : true;

    const updatedService = await serviceRepository.save(service);
    return NextResponse.json({ success: true, data: updatedService });
  } catch (error) {
    console.error('Update service error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const dataSource = await getDatabase();
    const serviceRepository = dataSource.getRepository(Service);

    const service = await serviceRepository.findOne({
      where: { id: parseInt(params.id) }
    });

    if (!service) {
      return NextResponse.json({ error: 'Service not found' }, { status: 404 });
    }

    await serviceRepository.remove(service);
    return NextResponse.json({ success: true, message: 'Service deleted successfully' });
  } catch (error) {
    console.error('Delete service error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { Portfolio } from '@/lib/entities';

export async function GET(request: NextRequest) {
  try {
    const dataSource = await getDatabase();
    const portfolioRepository = dataSource.getRepository(Portfolio);
    
    const portfolio = await portfolioRepository.find({
      where: { isActive: true },
      order: { sortOrder: 'ASC', createdAt: 'DESC' }
    });

    return NextResponse.json({ success: true, data: portfolio });
  } catch (error) {
    console.error('Get public portfolio error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

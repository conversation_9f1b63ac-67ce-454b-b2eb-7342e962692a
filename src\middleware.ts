import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getUserFromRequest } from './lib/auth';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if the request is for admin routes (except login)
  if (pathname.startsWith('/admin') && !pathname.startsWith('/admin/login')) {
    const user = getUserFromRequest(request);
    
    if (!user) {
      // Redirect to login if not authenticated
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }
    
    if (user.role !== 'admin') {
      // Redirect to login if not admin
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }
  }

  // If user is authenticated and trying to access login page, redirect to dashboard
  if (pathname === '/admin/login') {
    const user = getUserFromRequest(request);
    if (user && user.role === 'admin') {
      return NextResponse.redirect(new URL('/admin', request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/admin/:path*']
};

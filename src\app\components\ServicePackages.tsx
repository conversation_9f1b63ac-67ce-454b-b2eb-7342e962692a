"use client";

import { motion } from "framer-motion";

type Package = {
  title: string;
  price?: string;
  description: string;
  features: string[];
  popular?: boolean;
};

export default function ServicePackages() {
  const packages: Package[] = [
    {
      title: "Basic Web Presence",
      price: "$500",
      description: "Perfect for small businesses or individuals needing a professional online presence",
      features: [
        "Custom responsive website (up to 5 pages)",
        "Modern design with your branding",
        "Contact form integration",
        "Basic SEO setup",
        "1 month free support"
      ]
    },
    {
      title: "Business Growth",
      price: "$1,500",
      description: "Ideal for growing businesses requiring advanced functionality",
      features: [
        "Custom web application development",
        "User authentication system",
        "Database integration",
        "Content management system (CMS)",
        "Responsive design for all devices",
        "Performance optimization",
        "3 months free support"
      ],
      popular: true
    },
    {
      title: "Enterprise Solution",
      price: "$3,500",
      description: "Comprehensive solution for large businesses with complex requirements",
      features: [
        "Full-stack web application",
        "User authentication & authorization",
        "Database design & optimization",
        "API development & integration",
        "Admin dashboard",
        "Advanced analytics & reporting",
        "Cloud deployment",
        "6 months free support",
        "Performance monitoring"
      ]
    },
    {
      title: "Custom Project",
      description: "Tailored solution for your specific business needs",
      features: [
        "Fully customized to your requirements",
        "Flexible technology stack",
        "Dedicated project manager",
        "Ongoing development & maintenance",
        "24/7 support options",
        "Scalable architecture",
        "Regular updates & improvements"
      ]
    }
  ];

  return (
    <section id="services" className="min-h-screen flex items-center p-8 pt-16">
      <div className="max-w-6xl mx-auto w-full">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-4xl font-bold mb-16 text-center"
        >
          MY SERVICES
        </motion.h2>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {packages.map((pkg, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className={`border rounded-none p-8 flex flex-col ${
                pkg.popular 
                  ? "border-purple-500 bg-purple-900/10 relative transform -translate-y-2 shadow-lg" 
                  : "border-gray-700 dark:border-gray-300"
              }`}
            >
              {pkg.popular && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-purple-600 text-white px-4 py-1 text-sm font-bold rounded-full">
                  MOST POPULAR
                </div>
              )}
              
              <h3 className="text-2xl font-bold mb-4">{pkg.title}</h3>
              
              {pkg.price ? (
                <div className="text-3xl font-bold mb-4 text-purple-500">{pkg.price}</div>
              ) : (
                <div className="text-2xl font-bold mb-4 text-gray-500">Custom</div>
              )}
              
              <p className="text-gray-600 dark:text-gray-400 mb-6 flex-grow">{pkg.description}</p>
              
              <ul className="space-y-3 mb-8">
                {pkg.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <div className="w-5 h-5 mr-3 text-purple-500 flex-shrink-0">✓</div>
                    <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                  </li>
                ))}
              </ul>
              
              <button 
                className={`mt-auto py-3 px-6 font-medium rounded-none transition-default ${
                  pkg.popular 
                    ? "bg-purple-600 hover:bg-purple-700 text-white" 
                    : "bg-gray-800 dark:bg-gray-200 hover:bg-gray-700 dark:hover:bg-gray-300 text-white dark:text-black"
                }`}
              >
                Get Started
              </button>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { Portfolio } from '@/lib/entities';
import { getUserFromRequest } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const dataSource = await getDatabase();
    const portfolioRepository = dataSource.getRepository(Portfolio);
    
    const portfolio = await portfolioRepository.find({
      order: { sortOrder: 'ASC', createdAt: 'DESC' }
    });

    return NextResponse.json({ success: true, data: portfolio });
  } catch (error) {
    console.error('Get portfolio error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { title, description, shortDescription, image, images, projectUrl, githubUrl, technologies, category, client, completedDate, sortOrder, isActive, isFeatured } = body;

    if (!title || !description) {
      return NextResponse.json({ error: 'Title and description are required' }, { status: 400 });
    }

    const dataSource = await getDatabase();
    const portfolioRepository = dataSource.getRepository(Portfolio);

    const portfolio = portfolioRepository.create({
      title,
      description,
      shortDescription,
      image,
      images: images || [],
      projectUrl,
      githubUrl,
      technologies: technologies || [],
      category,
      client,
      completedDate: completedDate ? new Date(completedDate) : undefined,
      sortOrder: sortOrder || 0,
      isActive: isActive !== undefined ? isActive : true,
      isFeatured: isFeatured || false
    });

    const savedPortfolio = await portfolioRepository.save(portfolio);
    return NextResponse.json({ success: true, data: savedPortfolio }, { status: 201 });
  } catch (error) {
    console.error('Create portfolio error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

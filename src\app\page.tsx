"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import LoadingSpinner from "@/app/components/LoadingSpinner";
import Header from "@/app/components/Header";
import Footer from "@/app/components/Footer";
import SkillCard from "@/app/components/SkillCard";
import ProjectCard from "@/app/components/ProjectCard";
import ExperienceItem from "@/app/components/ExperienceItem";
import ContactForm from "@/app/components/ContactForm";
import ServicePackages from "@/app/components/ServicePackages";
import HeroSection from "@/app/components/HeroSection";

export default function Home() {
  const [darkMode, setDarkMode] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Check system preference for dark mode
    const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
    setDarkMode(prefersDark);
    
    // Listen for changes in system preference
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleChange = (e: MediaQueryListEvent) => setDarkMode(e.matches);
    
    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className={`min-h-screen transition-default ${darkMode ? "dark bg-black text-white" : "bg-white text-black"}`}>
      <Header />
      
      <HeroSection darkMode={darkMode} toggleDarkMode={toggleDarkMode} />
      
      {/* About Section */}
      <section id="about" className="min-h-screen flex items-center p-8 pt-16">
        <div className="max-w-6xl mx-auto grid md:grid-cols-2 gap-16">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold mb-8">ABOUT ME</h2>
            <div className="w-64 h-64 bg-gray-200 dark:bg-gray-800 rounded-none mb-8 flex items-center justify-center">
              <div className="text-6xl font-bold text-gray-400 dark:text-gray-600">CPC</div>
            </div>
            <p className="text-lg mb-6 text-gray-700 dark:text-gray-300">
              I'm a freelance full-stack developer based in Arusha, Tanzania, specializing in creating elegant and performant web applications.
            </p>
            <p className="text-lg mb-6 text-gray-700 dark:text-gray-300">
              Known as <span className="font-bold text-purple-600">CRAQINHO DIAWARA</span>, I'm not just a developer but also a creative professional with expertise in graphic design and video direction. This unique combination allows me to approach projects from both technical and artistic perspectives.
            </p>
            <div className="flex space-x-4 mt-8">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="w-10 h-10 border border-black dark:border-white flex items-center justify-center grayscale-hover transition-default">
                  <div className="w-6 h-6 bg-gray-800 dark:bg-gray-200"></div>
                </div>
              ))}
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="flex flex-col justify-center"
          >
            <h3 className="text-2xl font-bold mb-6">SKILLS</h3>
            <div className="grid grid-cols-3 gap-6">
              {["Next.js", "NestJS", "Laravel", "TypeScript", "JavaScript", "Flutter", "React", "Node.js", "MongoDB", "Graphic Design", "Video Editing", "UI/UX Design"].map((skill, index) => (
                <SkillCard key={skill} skill={skill} index={index} />
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Services Section */}
      <ServicePackages />

      {/* Portfolio Section */}
      <section id="portfolio" className="min-h-screen p-8 pt-16">
        <div className="max-w-6xl mx-auto">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-4xl font-bold mb-16 text-center"
          >
            PORTFOLIO
          </motion.h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <ProjectCard key={item} item={item} />
            ))}
          </div>
        </div>
      </section>

      {/* Experience Section */}
      <section id="experience" className="min-h-screen flex items-center p-8 pt-16">
        <div className="max-w-4xl mx-auto w-full">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-4xl font-bold mb-16 text-center"
          >
            EXPERIENCE
          </motion.h2>
          
          <div className="relative">
            {/* Vertical line */}
            <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-300 dark:bg-gray-700 transform translate-x-1/2"></div>
            
            {/* Timeline items */}
            {[
              { company: "BIVAC Company", role: "Senior Developer", period: "2022 - Present" },
              { company: "PSSSF", role: "Full-Stack Developer", period: "2020 - 2022" },
              { company: "Kemsa Consultancy", role: "Web Developer", period: "2018 - 2020" },
              { company: "Redleaf Investments", role: "Frontend Developer", period: "2016 - 2018" },
              { company: "Zeon Studios", role: "Junior Developer", period: "2014 - 2016" }
            ].map((exp, index) => (
              <ExperienceItem key={index} exp={exp} index={index} />
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="min-h-screen flex items-center p-8 pt-16 bg-black text-white dark:bg-white dark:text-black">
        <div className="max-w-4xl mx-auto w-full">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-4xl font-bold mb-16 text-center"
          >
            GET IN TOUCH
          </motion.h2>
          
          <div className="grid md:grid-cols-2 gap-16">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-bold mb-6">Let's Collaborate</h3>
              <p className="text-lg mb-8 text-gray-300 dark:text-gray-700">
                Have a project in mind? Feel free to reach out and let's create something amazing together.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-6 h-6 mr-4 bg-white dark:bg-black border border-white dark:border-black"></div>
                  <span>Arusha, Tanzania</span>
                </div>
                <div className="flex items-center">
                  <div className="w-6 h-6 mr-4 bg-white dark:bg-black border border-white dark:border-black"></div>
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center">
                  <div className="w-6 h-6 mr-4 bg-white dark:bg-black border border-white dark:border-black"></div>
                  <span>+255 787 574 355</span>
                </div>
              </div>
              
              <div className="flex space-x-6 mt-12">
                {[1, 2, 3].map((item) => (
                  <div key={item} className="w-12 h-12 border border-white dark:border-black flex items-center justify-center grayscale-hover transition-default hover:bg-white hover:text-black dark:hover:bg-black dark:hover:text-white cursor-pointer">
                    <div className="w-6 h-6 bg-white dark:bg-black"></div>
                  </div>
                ))}
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <ContactForm />
            </motion.div>
          </div>
        </div>
      </section>
      
      <Footer />
    </div>
  );
}
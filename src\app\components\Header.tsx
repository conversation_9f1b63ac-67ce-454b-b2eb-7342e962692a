"use client";

import { useState } from "react";
import { motion } from "framer-motion";

export default function Header() {
  const [isOpen, setIsOpen] = useState(false);

  const navItems = [
    { name: "Home", href: "#" },
    { name: "About", href: "#about" },
    { name: "Portfolio", href: "#portfolio" },
    { name: "Experience", href: "#experience" },
    { name: "Services", href: "#services" },
    { name: "Contact", href: "#contact" }
  ];

  return (
    <header className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-white/10 dark:bg-black/20 backdrop-blur-lg border border-gray-200/20 dark:border-gray-800/30 rounded-xl shadow-lg w-[90%] max-w-4xl mx-auto">
      <div className="px-6 py-4 flex justify-between items-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-xl font-bold text-white"
        >
          CPC
        </motion.div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex space-x-8 items-center">
          {navItems.map((item) => (
            <a
              key={item.name}
              href={item.href}
              className="text-gray-200 hover:text-white transition-colors font-medium"
            >
              {item.name}
            </a>
          ))}
          <a
            href="/admin"
            className="text-purple-400 hover:text-purple-300 transition-colors font-medium text-sm"
          >
            Admin
          </a>
        </nav>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden focus:outline-none"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="w-6 h-6 relative flex flex-col justify-between">
            <span className={`block w-full h-0.5 bg-white transition-all duration-300 ${isOpen ? 'rotate-45 translate-y-2.5' : ''}`}></span>
            <span className={`block w-full h-0.5 bg-white transition-all duration-300 ${isOpen ? 'opacity-0' : 'opacity-100'}`}></span>
            <span className={`block w-full h-0.5 bg-white transition-all duration-300 ${isOpen ? '-rotate-45 -translate-y-2.5' : ''}`}></span>
          </div>
        </button>

        {/* Mobile Navigation */}
        {isOpen && (
          <motion.nav
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="absolute top-full left-0 right-0 bg-white/20 dark:bg-black/30 backdrop-blur-lg border border-gray-200/20 dark:border-gray-800/30 rounded-b-xl md:hidden mt-1"
          >
            <div className="flex flex-col py-4">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="px-6 py-3 text-gray-200 hover:text-white hover:bg-white/10 dark:hover:bg-black/20 transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  {item.name}
                </a>
              ))}
              <a
                href="/admin"
                className="px-6 py-3 text-purple-400 hover:text-purple-300 hover:bg-white/10 dark:hover:bg-black/20 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                Admin
              </a>
            </div>
          </motion.nav>
        )}
      </div>
    </header>
  );
}
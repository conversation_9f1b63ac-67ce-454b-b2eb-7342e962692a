import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { Service } from '@/lib/entities';
import { getUserFromRequest } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const dataSource = await getDatabase();
    const serviceRepository = dataSource.getRepository(Service);
    
    const services = await serviceRepository.find({
      order: { sortOrder: 'ASC', createdAt: 'DESC' }
    });

    return NextResponse.json({ success: true, data: services });
  } catch (error) {
    console.error('Get services error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { title, description, price, features, icon, image, type, sortOrder, isActive } = body;

    if (!title || !description) {
      return NextResponse.json({ error: 'Title and description are required' }, { status: 400 });
    }

    const dataSource = await getDatabase();
    const serviceRepository = dataSource.getRepository(Service);

    const service = serviceRepository.create({
      title,
      description,
      price,
      features: features || [],
      icon,
      image,
      type: type || 'package',
      sortOrder: sortOrder || 0,
      isActive: isActive !== undefined ? isActive : true
    });

    const savedService = await serviceRepository.save(service);
    return NextResponse.json({ success: true, data: savedService }, { status: 201 });
  } catch (error) {
    console.error('Create service error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('services')
export class Service {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column('varchar')
  title!: string;

  @Column('text')
  description!: string;

  @Column('varchar', { nullable: true })
  price?: string;

  @Column('simple-json', { nullable: true })
  features?: string[];

  @Column('varchar', { nullable: true })
  icon?: string;

  @Column('varchar', { nullable: true })
  image?: string;

  @Column('varchar', { default: 'package' })
  type!: string; // package, service, consultation

  @Column('integer', { default: 0 })
  sortOrder!: number;

  @Column('boolean', { default: true })
  isActive!: boolean;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}

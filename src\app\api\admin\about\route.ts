import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { About } from '@/lib/entities';
import { getUserFromRequest } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const dataSource = await getDatabase();
    const aboutRepository = dataSource.getRepository(About);
    
    // Get the active about record (there should only be one)
    const about = await aboutRepository.findOne({
      where: { isActive: true }
    });

    return NextResponse.json({ success: true, data: about });
  } catch (error) {
    console.error('Get about error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { title, description, shortBio, profileImage, location, email, phone, website, socialLinks } = body;

    if (!title || !description) {
      return NextResponse.json({ error: 'Title and description are required' }, { status: 400 });
    }

    const dataSource = await getDatabase();
    const aboutRepository = dataSource.getRepository(About);

    // Deactivate existing about records
    await aboutRepository.update({}, { isActive: false });

    const about = aboutRepository.create({
      title,
      description,
      shortBio,
      profileImage,
      location,
      email,
      phone,
      website,
      socialLinks,
      isActive: true
    });

    const savedAbout = await aboutRepository.save(about);
    return NextResponse.json({ success: true, data: savedAbout }, { status: 201 });
  } catch (error) {
    console.error('Create about error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    if (!user || user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { title, description, shortBio, profileImage, location, email, phone, website, socialLinks } = body;

    if (!title || !description) {
      return NextResponse.json({ error: 'Title and description are required' }, { status: 400 });
    }

    const dataSource = await getDatabase();
    const aboutRepository = dataSource.getRepository(About);

    let about = await aboutRepository.findOne({
      where: { isActive: true }
    });

    if (!about) {
      // Create new if none exists
      about = aboutRepository.create({
        title,
        description,
        shortBio,
        profileImage,
        location,
        email,
        phone,
        website,
        socialLinks,
        isActive: true
      });
    } else {
      // Update existing
      about.title = title;
      about.description = description;
      about.shortBio = shortBio;
      about.profileImage = profileImage;
      about.location = location;
      about.email = email;
      about.phone = phone;
      about.website = website;
      about.socialLinks = socialLinks;
    }

    const updatedAbout = await aboutRepository.save(about);
    return NextResponse.json({ success: true, data: updatedAbout });
  } catch (error) {
    console.error('Update about error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

# <PERSON> - Portfolio

A stunning, modern, and minimalistic personal portfolio website for <PERSON>, a freelance full-stack software and web developer based in Arusha, Tanzania.

## Features

- Sleek monochrome aesthetic with bold typography
- Ample whitespace and smooth transitions
- Responsive design that works on all devices
- Dark & Light mode toggle
- Animated elements using Framer Motion
- Modern sans-serif font (Space Grotesk)

## Tech Stack

- [Next.js](https://nextjs.org/)
- [TypeScript](https://www.typescriptlang.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Framer Motion](https://www.framer.com/motion/)

## Sections

1. **Hero Section** - Full-screen layout with name and tagline
2. **About Section** - Introduction and skills
3. **Portfolio Section** - Project showcase
4. **Experience Section** - Professional timeline
5. **Contact Section** - Get in touch form

## Development

To run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Deployment

The portfolio can be deployed to [Vercel](https://vercel.com/) with a single click.
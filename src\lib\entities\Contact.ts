import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('contacts')
export class Contact {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  name!: string;

  @Column()
  email!: string;

  @Column({ nullable: true })
  phone?: string;

  @Column({ nullable: true })
  subject?: string;

  @Column('text')
  message!: string;

  @Column({ default: 'unread' })
  status!: string; // unread, read, replied, archived

  @Column({ nullable: true })
  notes?: string;

  @Column({ default: false })
  isImportant!: boolean;

  @Column({ nullable: true })
  repliedAt?: Date;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}

import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('contacts')
export class Contact {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column('varchar')
  name!: string;

  @Column('varchar')
  email!: string;

  @Column('varchar', { nullable: true })
  phone?: string;

  @Column('varchar', { nullable: true })
  subject?: string;

  @Column('text')
  message!: string;

  @Column('varchar', { default: 'unread' })
  status!: string; // unread, read, replied, archived

  @Column('text', { nullable: true })
  notes?: string;

  @Column('boolean', { default: false })
  isImportant!: boolean;

  @Column('datetime', { nullable: true })
  repliedAt?: Date;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}

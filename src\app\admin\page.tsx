"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';

interface Stats {
  skills: number;
  portfolio: number;
  experience: number;
  messages: number;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<Stats>({
    skills: 0,
    portfolio: 0,
    experience: 0,
    messages: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      // We'll implement these API endpoints later
      const [skillsRes, portfolioRes, experienceRes, messagesRes] = await Promise.allSettled([
        fetch('/api/admin/skills'),
        fetch('/api/admin/portfolio'),
        fetch('/api/admin/experience'),
        fetch('/api/admin/contact')
      ]);

      setStats({
        skills: skillsRes.status === 'fulfilled' ? (await skillsRes.value.json()).data?.length || 0 : 0,
        portfolio: portfolioRes.status === 'fulfilled' ? (await portfolioRes.value.json()).data?.length || 0 : 0,
        experience: experienceRes.status === 'fulfilled' ? (await experienceRes.value.json()).data?.length || 0 : 0,
        messages: messagesRes.status === 'fulfilled' ? (await messagesRes.value.json()).data?.length || 0 : 0,
      });
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    { title: 'Skills', value: stats.skills, icon: '🛠️', href: '/admin/skills', color: 'bg-blue-500' },
    { title: 'Portfolio Projects', value: stats.portfolio, icon: '🎨', href: '/admin/portfolio', color: 'bg-green-500' },
    { title: 'Experience', value: stats.experience, icon: '💼', href: '/admin/experience', color: 'bg-yellow-500' },
    { title: 'Messages', value: stats.messages, icon: '📧', href: '/admin/contact', color: 'bg-red-500' },
  ];

  const quickActions = [
    { title: 'Add New Skill', href: '/admin/skills/new', icon: '➕', description: 'Add a new skill to your profile' },
    { title: 'Create Portfolio Item', href: '/admin/portfolio/new', icon: '🎨', description: 'Showcase a new project' },
    { title: 'Add Experience', href: '/admin/experience/new', icon: '💼', description: 'Add work experience' },
    { title: 'Update About Me', href: '/admin/about', icon: '👤', description: 'Update your personal information' },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Dashboard</h1>
        <p className="text-gray-400">Welcome back! Here's an overview of your portfolio.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => (
          <motion.div
            key={card.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <Link href={card.href}>
              <div className="bg-gray-900 border border-gray-800 rounded-lg p-6 hover:border-purple-500 transition-colors cursor-pointer">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">{card.title}</p>
                    <p className="text-3xl font-bold mt-1">
                      {loading ? '...' : card.value}
                    </p>
                  </div>
                  <div className={`w-12 h-12 ${card.color} rounded-lg flex items-center justify-center text-2xl`}>
                    {card.icon}
                  </div>
                </div>
              </div>
            </Link>
          </motion.div>
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {quickActions.map((action, index) => (
            <motion.div
              key={action.title}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Link href={action.href}>
                <div className="bg-gray-900 border border-gray-800 rounded-lg p-4 hover:border-purple-500 transition-colors cursor-pointer">
                  <div className="flex items-center">
                    <div className="text-2xl mr-4">{action.icon}</div>
                    <div>
                      <h3 className="font-semibold">{action.title}</h3>
                      <p className="text-gray-400 text-sm">{action.description}</p>
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Recent Activity</h2>
        <div className="bg-gray-900 border border-gray-800 rounded-lg p-6">
          <p className="text-gray-400 text-center py-8">
            Recent activity will be displayed here once you start managing your content.
          </p>
        </div>
      </div>
    </div>
  );
}

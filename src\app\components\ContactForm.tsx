"use client";

import { motion } from "framer-motion";

export default function ContactForm() {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Form submission logic would go here
    alert("Thank you for your message! I'll get back to you soon.");
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <input
          type="text"
          placeholder="Name"
          className="w-full p-4 bg-transparent border border-gray-600 dark:border-gray-400 focus:border-purple-500 dark:focus:border-purple-500 focus:outline-none transition-default text-white placeholder-gray-400"
          required
        />
      </div>
      <div>
        <input
          type="email"
          placeholder="Email"
          className="w-full p-4 bg-transparent border border-gray-600 dark:border-gray-400 focus:border-purple-500 dark:focus:border-purple-500 focus:outline-none transition-default text-white placeholder-gray-400"
          required
        />
      </div>
      <div>
        <textarea
          placeholder="Message"
          rows={5}
          className="w-full p-4 bg-transparent border border-gray-600 dark:border-gray-400 focus:border-purple-500 dark:focus:border-purple-500 focus:outline-none transition-default resize-none text-white placeholder-gray-400"
          required
        ></textarea>
      </div>
      <motion.button
        whileHover={{ 
          scale: 1.05,
          backgroundColor: "rgba(147, 51, 234, 0.9)", // Purple background
          color: "white",
        }}
        whileTap={{ scale: 0.95 }}
        type="submit"
        className="px-8 py-4 bg-purple-600 hover:bg-purple-700 font-medium text-lg w-full transition-default text-white rounded-none"
      >
        Send Message
      </motion.button>
    </form>
  );
}
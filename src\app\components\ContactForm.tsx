"use client";

import { useState } from "react";
import { motion } from "framer-motion";

export default function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/admin/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setSuccess(true);
        setFormData({ name: '', email: '', phone: '', subject: '', message: '' });
        setTimeout(() => setSuccess(false), 5000);
      } else {
        alert('Failed to send message. Please try again.');
      }
    } catch (error) {
      alert('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {success && (
        <div className="bg-green-500/10 border border-green-500/20 text-green-400 px-4 py-3 rounded">
          Thank you for your message! I'll get back to you soon.
        </div>
      )}

      <div>
        <input
          type="text"
          name="name"
          placeholder="Name"
          value={formData.name}
          onChange={handleChange}
          className="w-full p-4 bg-transparent border border-gray-600 dark:border-gray-400 focus:border-purple-500 dark:focus:border-purple-500 focus:outline-none transition-default text-white placeholder-gray-400"
          required
        />
      </div>
      <div>
        <input
          type="email"
          name="email"
          placeholder="Email"
          value={formData.email}
          onChange={handleChange}
          className="w-full p-4 bg-transparent border border-gray-600 dark:border-gray-400 focus:border-purple-500 dark:focus:border-purple-500 focus:outline-none transition-default text-white placeholder-gray-400"
          required
        />
      </div>
      <div>
        <input
          type="tel"
          name="phone"
          placeholder="Phone (optional)"
          value={formData.phone}
          onChange={handleChange}
          className="w-full p-4 bg-transparent border border-gray-600 dark:border-gray-400 focus:border-purple-500 dark:focus:border-purple-500 focus:outline-none transition-default text-white placeholder-gray-400"
        />
      </div>
      <div>
        <input
          type="text"
          name="subject"
          placeholder="Subject (optional)"
          value={formData.subject}
          onChange={handleChange}
          className="w-full p-4 bg-transparent border border-gray-600 dark:border-gray-400 focus:border-purple-500 dark:focus:border-purple-500 focus:outline-none transition-default text-white placeholder-gray-400"
        />
      </div>
      <div>
        <textarea
          name="message"
          placeholder="Message"
          value={formData.message}
          onChange={handleChange}
          rows={5}
          className="w-full p-4 bg-transparent border border-gray-600 dark:border-gray-400 focus:border-purple-500 dark:focus:border-purple-500 focus:outline-none transition-default resize-none text-white placeholder-gray-400"
          required
        ></textarea>
      </div>
      <motion.button
        whileHover={{
          scale: 1.05,
          backgroundColor: "rgba(147, 51, 234, 0.9)", // Purple background
          color: "white",
        }}
        whileTap={{ scale: 0.95 }}
        type="submit"
        disabled={loading}
        className="px-8 py-4 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 font-medium text-lg w-full transition-default text-white rounded-none"
      >
        {loading ? 'Sending...' : 'Send Message'}
      </motion.button>
    </form>
  );
}
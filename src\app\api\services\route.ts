import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { Service } from '@/lib/entities';

export async function GET(request: NextRequest) {
  try {
    const dataSource = await getDatabase();
    const serviceRepository = dataSource.getRepository(Service);
    
    const services = await serviceRepository.find({
      where: { isActive: true },
      order: { sortOrder: 'ASC', createdAt: 'DESC' }
    });

    return NextResponse.json({ success: true, data: services });
  } catch (error) {
    console.error('Get public services error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

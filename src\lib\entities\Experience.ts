import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('experience')
export class Experience {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column('varchar')
  company!: string;

  @Column('varchar')
  position!: string;

  @Column('text', { nullable: true })
  description?: string;

  @Column('date')
  startDate!: Date;

  @Column('date', { nullable: true })
  endDate?: Date;

  @Column('boolean', { default: false })
  isCurrent!: boolean;

  @Column('varchar', { nullable: true })
  location?: string;

  @Column('simple-json', { nullable: true })
  technologies?: string[];

  @Column('simple-json', { nullable: true })
  achievements?: string[];

  @Column('varchar', { nullable: true })
  companyUrl?: string;

  @Column('varchar', { nullable: true })
  companyLogo?: string;

  @Column('integer', { default: 0 })
  sortOrder!: number;

  @Column('boolean', { default: true })
  isActive!: boolean;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}

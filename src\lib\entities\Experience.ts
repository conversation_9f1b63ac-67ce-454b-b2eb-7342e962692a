import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('experience')
export class Experience {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  company!: string;

  @Column()
  position!: string;

  @Column('text', { nullable: true })
  description?: string;

  @Column({ type: 'date' })
  startDate!: Date;

  @Column({ type: 'date', nullable: true })
  endDate?: Date;

  @Column({ default: false })
  isCurrent!: boolean;

  @Column({ nullable: true })
  location?: string;

  @Column('simple-json', { nullable: true })
  technologies?: string[];

  @Column('simple-json', { nullable: true })
  achievements?: string[];

  @Column({ nullable: true })
  companyUrl?: string;

  @Column({ nullable: true })
  companyLogo?: string;

  @Column({ default: 0 })
  sortOrder!: number;

  @Column({ default: true })
  isActive!: boolean;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
}

"use client";

import { motion } from "framer-motion";

export default function HeroButton() {
  return (
    <motion.button
      whileHover={{ 
        scale: 1.05,
        backgroundColor: "rgba(147, 51, 234, 0.9)", // Purple background
        color: "white",
      }}
      whileTap={{ scale: 0.95 }}
      className="px-8 py-4 border-2 border-purple-500 font-medium text-lg rounded-none transition-default text-white"
    >
      View My Projects
    </motion.button>
  );
}